# HTTP Module Reorganization Migration Guide

This document provides guidance for migrating from the old HTTP module structure to the new organized architecture.

## Overview of Changes

The HTTP module has been reorganized for better maintainability and clearer separation of concerns:

### Key Improvements

1. **Organized Directory Structure**: Related functionality grouped in logical directories
2. **Clear Separation of Concerns**: Each directory has a focused purpose
3. **Reduced Root Clutter**: Only essential files remain in the root directory
4. **Better Import Paths**: More intuitive and discoverable import structure
5. **Legacy Compatibility**: Backward compatibility maintained with deprecation warnings

### New Directory Structure

```
erp/http/
├── __init__.py                 # Clean, minimal exports
├── MIGRATION.md               # This migration guide
├── interfaces.py              # All interface definitions
├── metadata.py               # Route metadata and enums
├── decorators.py             # Route decorators (@route, @systemRoute)
│
├── core/                     # Core functionality
│   ├── __init__.py
│   ├── jsonrpc.py           # JSON-RPC implementation
│   └── exceptions.py        # HTTP-specific exceptions
│
├── auth/                     # Authentication & authorization
│   ├── __init__.py
│   ├── types.py             # AuthType enum and types
│   ├── handlers.py          # Authentication handlers
│   └── middleware.py        # Auth middleware
│
├── middleware/               # Request/response middleware
│   ├── __init__.py
│   ├── base.py              # Base middleware class
│   ├── pipeline.py          # Middleware pipeline
│   ├── cors.py              # CORS middleware
│   ├── request.py           # Request processing
│   └── response.py          # Response processing
│
├── controllers/              # Controller classes
│   ├── __init__.py
│   ├── base.py              # BaseController
│   ├── mixins.py            # Controller mixins
│   ├── controller.py        # Specialized controllers
│   ├── legacy.py            # Legacy controller (deprecated)
│   └── registry.py          # Controller registry
│
├── registries/               # Route registries
│   ├── __init__.py
│   ├── system.py            # System route registry
│   ├── database.py          # Database route registry
│   └── manager.py           # Registry manager
│
├── adapters/                 # Framework adapters
│   ├── __init__.py
│   ├── base.py              # Base adapter
│   ├── fastapi.py           # FastAPI adapter
│   └── response.py          # Response transformers
│
├── services/                 # Business services
│   ├── __init__.py
│   ├── discovery.py         # Route discovery
│   ├── validation.py        # Route validation
│   ├── health.py            # Health checking
│   └── factories.py         # Object factories
│
├── integration/              # Framework integration
│   ├── __init__.py
│   ├── fastapi.py           # FastAPI integration
│   └── setup.py             # Integration setup
│
└── tests/                    # Test files
    └── test_integration.py
```

## Migration Steps

### 1. Update Imports

**Old (deprecated):**
```python
from erp.http.auth import AuthType, require_auth
from erp.http.cors import cors_handler
from erp.http.controller import Controller
from erp.http.jsonrpc import JsonRpcHandler
from erp.http.registry import get_system_route_registry
from erp.http.discovery import get_route_discovery_service
from erp.http.validation import get_route_validator
from erp.http.health import get_health_checker
from erp.http.factories import get_route_handler_factory
```

**New (organized):**
```python
from erp.http.auth import AuthType, require_auth  # Now in auth/ directory
from erp.http.middleware import cors_handler  # Now in middleware/
from erp.http.controllers import Controller  # Now in controllers/
from erp.http.core import JsonRpcHandler  # Now in core/
from erp.http.registries import get_system_route_registry  # Now in registries/
from erp.http.services import get_route_discovery_service  # Now in services/
from erp.http.services import get_route_validator  # Now in services/
from erp.http.services import get_health_checker  # Now in services/
from erp.http.services import get_route_handler_factory  # Now in services/
```

**Still works (main imports):**
```python
from erp.http import route, Controller, AuthType, JsonRpcHandler  # Main exports unchanged
```

### 2. Controller Updates

**Old Monolithic Controller:**
```python
class MyController(Controller):
    def my_route(self, request):
        # All functionality mixed together
        params = self.params
        user = self.get_current_user()
        html = self.render_template('template.html', {})
        return html
```

**New Modular Controller:**
```python
# Option 1: Use full Controller (same as before)
class MyController(Controller):
    def my_route(self, request):
        # Same code works
        params = self.params
        user = self.get_current_user()
        html = self.render_template('template.html', {})
        return html

# Option 2: Use specialized controllers
class MyAPIController(APIController):  # Only API functionality
    def my_api_route(self, request):
        self.validate_required_params(['param1'])
        return self.json_response({'status': 'ok'})

class MyTemplateController(TemplateController):  # Only template functionality
    def my_template_route(self, request):
        return self.render_template('template.html', {})
```

### 3. Route Registration

**Old:**
```python
registry = get_system_route_registry()
registry.register_route('/path', handler, methods=['GET'])
```

**New:**
```python
from erp.http.registries import get_system_route_registry
from erp.http.factories import get_route_info_factory
from erp.http.interfaces import RouteScope

registry = get_system_route_registry()
factory = get_route_info_factory()

route_info = factory.create_route_info(
    path='/path',
    handler=handler,
    methods=['GET'],
    scope=RouteScope.SYSTEM
)

await registry.register_route(route_info)
```

### 4. Middleware Usage

**Old (monolithic processors):**
```python
from erp.http.request_processor import RequestProcessor
from erp.http.response_processor import ResponseProcessor

# Manual processing
processed_request = RequestProcessor.process(request)
response = handler(processed_request)
processed_response = ResponseProcessor.process(response)
```

**New (middleware pipeline):**
```python
from erp.http.middleware import get_middleware_pipeline

pipeline = get_middleware_pipeline()
response = await pipeline.execute_pipeline(request, route_info, handler)
```

### 5. FastAPI Integration

**Old:**
```python
from erp.http.fastapi_integration import RouteIntegration

RouteIntegration.register_routes_with_fastapi(app, registry)
```

**New:**
```python
from erp.http.integration import setup_http_routes

await setup_http_routes(app)
```

## Backward Compatibility

The refactoring maintains backward compatibility:

### Existing Code Continues to Work

- All existing decorators (`@route`, `@systemRoute`) work unchanged
- Controller classes work with the same API
- Import paths are maintained with deprecation warnings

### Deprecation Warnings

Some modules now issue deprecation warnings:

```python
# This works but shows a warning
from erp.http.registry import SystemRouteRegistry

# Use this instead
from erp.http.registries import SystemRouteRegistry
```

## New Features

### 1. Validation Service

```python
from erp.http.validation import get_route_validator

validator = get_route_validator()
errors = await validator.validate_route(route_info)
```

### 2. Health Checks

```python
from erp.http.health import get_health_checker

health_checker = get_health_checker()
health = await health_checker.check_registry_health(registry)
```

### 3. Route Discovery

```python
from erp.http.discovery import get_route_discovery_service

discovery = get_route_discovery_service()
routes = await discovery.discover_routes('my.module', RouteScope.SYSTEM)
```

### 4. Custom Middleware

```python
from erp.http.middleware import BaseMiddleware, get_middleware_pipeline

class CustomMiddleware(BaseMiddleware):
    def __init__(self):
        super().__init__(priority=50)
    
    async def process_request(self, request, route_info):
        # Custom request processing
        return request
    
    async def process_response(self, response, route_info):
        # Custom response processing
        return response

pipeline = get_middleware_pipeline()
await pipeline.add_middleware(CustomMiddleware())
```

## Testing Updates

### Old Tests

```python
def test_route():
    registry = get_system_route_registry()
    registry.register_route('/test', handler)
    routes = registry.get_routes()
    assert '/test' in routes
```

### New Tests

```python
@pytest.mark.asyncio
async def test_route():
    from erp.http.registries import get_system_route_registry, reset_system_route_registry
    from erp.http.factories import get_route_info_factory
    
    await reset_system_route_registry()  # Clean state
    
    registry = get_system_route_registry()
    factory = get_route_info_factory()
    
    route_info = factory.create_route_info(
        path='/test',
        handler=handler,
        scope=RouteScope.SYSTEM
    )
    
    await registry.register_route(route_info)
    routes = await registry.get_routes()
    assert '/test' in routes
```

## Performance Improvements

The new architecture provides several performance benefits:

1. **Lazy Loading**: Components are initialized only when needed
2. **Async-First**: All operations are async by default
3. **Caching**: Health checks and validation results are cached
4. **Pipeline Optimization**: Middleware can be conditionally applied

## Troubleshooting

### Common Issues

1. **Import Errors**: Update import paths as shown above
2. **Async Errors**: Ensure you're using `await` with new async methods
3. **Registry Errors**: Use the new RouteInfo objects instead of raw dictionaries

### Getting Help

- Check the deprecation warnings for guidance
- Review the test files for usage examples
- Use the new validation service to check your routes

## Future Deprecations

The following will be deprecated in future versions:

- `erp.http.registry` module (use `erp.http.registries`)
- `erp.http.controller` module (use `erp.http.controllers`)
- Synchronous route registration methods

Plan to migrate to the new APIs to avoid future breaking changes.
