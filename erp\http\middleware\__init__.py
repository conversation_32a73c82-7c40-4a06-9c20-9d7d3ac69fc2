"""
Middleware pipeline system for HTTP routes
Provides composable middleware components for request/response processing
"""

from .pipeline import MiddlewarePipeline, get_middleware_pipeline
from .base import BaseMiddleware
from .cors import CorsMiddleware, cors_handler
from .request import RequestProcessing<PERSON>iddleware, RequestProcessor, create_request_context
from .response import ResponseProcessingMiddleware, ResponseProcessor

# Note: AuthMiddleware is imported from erp.http.auth to avoid circular imports

__all__ = [
    'MiddlewarePipeline',
    'get_middleware_pipeline',
    'BaseMiddleware',

    'CorsMiddleware',
    'RequestProcessingMiddleware',
    'ResponseProcessingMiddleware',
    'cors_handler',  # Legacy compatibility
    'RequestProcessor',  # Legacy compatibility
    'ResponseProcessor',  # Legacy compatibility
    'create_request_context'  # Legacy compatibility
]
