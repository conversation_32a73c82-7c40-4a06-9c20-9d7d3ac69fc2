"""
HTTP integration module
Provides high-level integration functions for setting up the HTTP routing system
"""

import asyncio
from typing import Any, Dict, List, Optional

from ..registries import get_system_route_registry, get_database_route_manager
from ..middleware import get_middleware_pipeline, BaseMiddleware, AuthMiddleware, CorsMiddleware
from ..middleware import RequestProcessingMiddleware, ResponseProcessingMiddleware
from ..adapters import get_adapter_registry, FastAPIRouteAdapter
from ..services import get_route_discovery_service, get_route_organizer
from ..services import get_route_validator
from ..services import get_health_checker
from ..interfaces import RouteScope
from ...logging import get_logger

logger = get_logger(__name__)


class HTTPSystemManager:
    """Manages the entire HTTP routing system"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self._initialized = False
        self._middleware_configured = False
        self._adapters_configured = False
    
    async def initialize(self, config: Optional[Dict[str, Any]] = None) -> None:
        """
        Initialize the HTTP routing system
        
        Args:
            config: Configuration dictionary
        """
        if self._initialized:
            self.logger.warning("HTTP system already initialized")
            return
        
        try:
            self.logger.info("Initializing HTTP routing system")
            
            # Setup middleware pipeline
            await self._setup_middleware_pipeline(config)
            
            # Setup adapters
            await self._setup_adapters(config)
            
            # Initialize services
            await self._initialize_services(config)
            
            self._initialized = True
            self.logger.info("HTTP routing system initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize HTTP system: {e}")
            raise
    
    async def setup_fastapi_integration(self, app: Any, config: Optional[Dict[str, Any]] = None) -> None:
        """
        Setup FastAPI integration
        
        Args:
            app: FastAPI application instance
            config: Configuration dictionary
        """
        try:
            self.logger.info("Setting up FastAPI integration")
            
            # Ensure system is initialized
            if not self._initialized:
                await self.initialize(config)
            
            # Get system routes
            system_registry = get_system_route_registry()
            system_routes = await system_registry.get_routes()
            
            # Register routes with FastAPI
            from .adapters.fastapi import get_fastapi_integration_manager
            integration_manager = get_fastapi_integration_manager()
            await integration_manager.register_routes_with_app(app, system_routes)
            
            self.logger.info("FastAPI integration setup complete")
            
        except Exception as e:
            self.logger.error(f"Failed to setup FastAPI integration: {e}")
            raise
    
    async def discover_and_register_routes(
        self, 
        sources: List[str], 
        scope: RouteScope = RouteScope.SYSTEM
    ) -> int:
        """
        Discover and register routes from sources
        
        Args:
            sources: List of source identifiers (modules, packages, etc.)
            scope: Route scope
            
        Returns:
            Number of routes registered
        """
        try:
            discovery_service = get_route_discovery_service()
            total_registered = 0
            
            for source in sources:
                self.logger.debug(f"Discovering routes from {source}")
                routes = await discovery_service.discover_routes(source, scope)
                
                # Register routes with appropriate registry
                if scope == RouteScope.SYSTEM:
                    registry = get_system_route_registry()
                    for route_info in routes:
                        await registry.register_route(route_info)
                        total_registered += 1
                else:
                    # For database/addon routes, register with database manager
                    db_manager = get_database_route_manager()
                    # This would need database context - simplified for now
                    self.logger.debug(f"Database/addon routes discovered but not registered: {len(routes)}")
            
            self.logger.info(f"Discovered and registered {total_registered} routes")
            return total_registered
            
        except Exception as e:
            self.logger.error(f"Failed to discover and register routes: {e}")
            return 0
    
    async def validate_system(self) -> Dict[str, Any]:
        """
        Validate the entire HTTP system
        
        Returns:
            Validation results
        """
        try:
            validator = get_route_validator()
            results = {}
            
            # Validate system registry
            system_registry = get_system_route_registry()
            system_validation = await validator.validate_registry(system_registry)
            if system_validation:
                results['system_routes'] = system_validation
            
            # Validate database registries
            db_manager = get_database_route_manager()
            db_registries = await db_manager.get_all_registries()
            
            for db_name, registry in db_registries.items():
                db_validation = await validator.validate_registry(registry)
                if db_validation:
                    results[f'database_{db_name}'] = db_validation
            
            return results
            
        except Exception as e:
            self.logger.error(f"System validation failed: {e}")
            return {'error': str(e)}
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on the HTTP system
        
        Returns:
            Health check results
        """
        try:
            health_checker = get_health_checker()
            results = {}
            
            # Check system registry health
            system_registry = get_system_route_registry()
            system_health = await health_checker.check_registry_health(system_registry)
            results['system_registry'] = system_health
            
            # Check database registries health
            db_manager = get_database_route_manager()
            db_registries = await db_manager.get_all_registries()
            
            for db_name, registry in db_registries.items():
                db_health = await health_checker.check_registry_health(registry)
                results[f'database_{db_name}'] = db_health
            
            return results
            
        except Exception as e:
            self.logger.error(f"Health check failed: {e}")
            return {'error': str(e)}
    
    async def get_system_stats(self) -> Dict[str, Any]:
        """
        Get system statistics
        
        Returns:
            System statistics
        """
        try:
            stats = {}
            
            # System registry stats
            system_registry = get_system_route_registry()
            stats['system_registry'] = system_registry.get_stats()
            
            # Database registry stats
            db_manager = get_database_route_manager()
            db_registries = await db_manager.get_all_registries()
            stats['database_registries'] = {
                db_name: registry.get_stats() 
                for db_name, registry in db_registries.items()
            }
            
            # Middleware stats
            middleware_pipeline = get_middleware_pipeline()
            stats['middleware'] = middleware_pipeline.get_stats()
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Failed to get system stats: {e}")
            return {'error': str(e)}
    
    async def _setup_middleware_pipeline(self, config: Optional[Dict[str, Any]]) -> None:
        """Setup middleware pipeline"""
        if self._middleware_configured:
            return
        
        pipeline = get_middleware_pipeline()
        
        # Add default middleware in order of priority
        await pipeline.add_middleware(AuthMiddleware(priority=20))
        await pipeline.add_middleware(CorsMiddleware(priority=15))
        await pipeline.add_middleware(RequestProcessingMiddleware(priority=70))
        await pipeline.add_middleware(ResponseProcessingMiddleware(priority=85))
        
        # Add custom middleware from config
        if config and 'middleware' in config:
            for middleware_config in config['middleware']:
                # This would create middleware from config - simplified for now
                pass
        
        self._middleware_configured = True
        self.logger.debug("Middleware pipeline configured")
    
    async def _setup_adapters(self, config: Optional[Dict[str, Any]]) -> None:
        """Setup route adapters"""
        if self._adapters_configured:
            return
        
        adapter_registry = get_adapter_registry()
        
        # Register FastAPI adapter
        fastapi_adapter = FastAPIRouteAdapter()
        adapter_registry.register_adapter('fastapi', fastapi_adapter)
        
        # Register other adapters from config
        if config and 'adapters' in config:
            for adapter_config in config['adapters']:
                # This would create adapters from config - simplified for now
                pass
        
        self._adapters_configured = True
        self.logger.debug("Route adapters configured")
    
    async def _initialize_services(self, config: Optional[Dict[str, Any]]) -> None:
        """Initialize services"""
        # Services are initialized when first accessed
        # This method can be used for any additional service setup
        pass


# Global system manager
_system_manager = HTTPSystemManager()


def get_http_system_manager() -> HTTPSystemManager:
    """Get the global HTTP system manager"""
    return _system_manager


# Convenience functions
async def setup_http_routes(app: Any, config: Optional[Dict[str, Any]] = None) -> None:
    """
    Setup HTTP routes with FastAPI application
    
    Args:
        app: FastAPI application instance
        config: Configuration dictionary
    """
    manager = get_http_system_manager()
    await manager.setup_fastapi_integration(app, config)


async def initialize_http_system(config: Optional[Dict[str, Any]] = None) -> None:
    """
    Initialize the HTTP routing system
    
    Args:
        config: Configuration dictionary
    """
    manager = get_http_system_manager()
    await manager.initialize(config)


async def discover_routes(sources: List[str], scope: RouteScope = RouteScope.SYSTEM) -> int:
    """
    Discover and register routes from sources
    
    Args:
        sources: List of source identifiers
        scope: Route scope
        
    Returns:
        Number of routes registered
    """
    manager = get_http_system_manager()
    return await manager.discover_and_register_routes(sources, scope)
