"""
Integration tests for the refactored HTTP module
Tests the complete system functionality
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock

from ..interfaces import RouteInfo, RouteScope
from ..metadata import RouteType, AuthType
from ..registries import get_system_route_registry, reset_system_route_registry
from ..factories import get_route_info_factory
from ..middleware import get_middleware_pipeline, reset_middleware_pipeline
from ..controllers import Controller, get_controller_registry
from ..decorators import route, systemRoute
from ..integration import get_http_system_manager, initialize_http_system


class TestHTTPIntegration:
    """Test HTTP system integration"""
    
    @pytest.fixture(autouse=True)
    async def setup_and_teardown(self):
        """Setup and teardown for each test"""
        # Reset registries before each test
        await reset_system_route_registry()
        await reset_middleware_pipeline()
        get_controller_registry().clear()
        
        yield
        
        # Cleanup after each test
        await reset_system_route_registry()
        await reset_middleware_pipeline()
        get_controller_registry().clear()
    
    @pytest.mark.asyncio
    async def test_system_initialization(self):
        """Test HTTP system initialization"""
        manager = get_http_system_manager()
        
        # Initialize system
        await initialize_http_system()
        
        # Check that middleware pipeline is configured
        pipeline = get_middleware_pipeline()
        middleware_info = await pipeline.get_middleware_info()
        
        assert len(middleware_info) > 0
        assert any('Auth' in mw['name'] for mw in middleware_info)
        assert any('Cors' in mw['name'] for mw in middleware_info)
    
    @pytest.mark.asyncio
    async def test_route_registration_and_discovery(self):
        """Test route registration and discovery"""
        # Create a test route
        @route('/test', methods=['GET'])
        async def test_handler(request):
            return {'message': 'test'}
        
        # Create route info
        factory = get_route_info_factory()
        route_info = factory.create_route_info(
            path='/test',
            handler=test_handler,
            methods=['GET'],
            scope=RouteScope.SYSTEM,
            source='test_module'
        )
        
        # Register route
        registry = get_system_route_registry()
        success = await registry.register_route(route_info)
        assert success
        
        # Verify route is registered
        routes = await registry.get_routes()
        assert '/test' in routes
        assert len(routes['/test']) == 1
        assert routes['/test'][0].handler == test_handler
    
    @pytest.mark.asyncio
    async def test_controller_integration(self):
        """Test controller integration"""
        class TestController(Controller):
            @route('/controller/test', methods=['GET'])
            async def test_method(self, request):
                return self.json_response({'message': 'controller test'})
        
        # Register controller
        controller_registry = get_controller_registry()
        controller_registry.register_controller('test', TestController)
        
        # Verify controller is registered
        controller_class = controller_registry.get_controller('test')
        assert controller_class == TestController
        
        # Create controller instance
        controller = controller_registry.create_controller('test')
        assert isinstance(controller, TestController)
    
    @pytest.mark.asyncio
    async def test_middleware_pipeline(self):
        """Test middleware pipeline execution"""
        # Create a test route
        async def test_handler(request):
            return {'message': 'test'}
        
        route_info = RouteInfo(
            path='/test',
            handler=test_handler,
            methods=['GET'],
            route_type=RouteType.HTTP,
            auth=AuthType.NONE,
            scope=RouteScope.SYSTEM,
            metadata={},
            source='test'
        )
        
        # Create mock request
        mock_request = Mock()
        mock_request.method = 'GET'
        mock_request.query_params = {}
        mock_request.headers = {}
        
        # Execute through pipeline
        pipeline = get_middleware_pipeline()
        result = await pipeline.execute_pipeline(mock_request, route_info, test_handler)
        
        # Verify result
        assert result is not None
    
    @pytest.mark.asyncio
    async def test_route_validation(self):
        """Test route validation"""
        from ..validation import get_route_validator
        
        # Create valid route
        valid_route = RouteInfo(
            path='/valid',
            handler=lambda x: x,
            methods=['GET'],
            route_type=RouteType.HTTP,
            auth=AuthType.USER,
            scope=RouteScope.SYSTEM,
            metadata={},
            source='test'
        )
        
        validator = get_route_validator()
        errors = await validator.validate_route(valid_route)
        assert len(errors) == 0
        
        # Create invalid route
        invalid_route = RouteInfo(
            path='',  # Invalid empty path
            handler=None,  # Invalid None handler
            methods=[],  # Invalid empty methods
            route_type=RouteType.HTTP,
            auth=AuthType.USER,
            scope=RouteScope.SYSTEM,
            metadata={},
            source='test'
        )
        
        errors = await validator.validate_route(invalid_route)
        assert len(errors) > 0
    
    @pytest.mark.asyncio
    async def test_health_checks(self):
        """Test health check functionality"""
        from ..health import get_health_checker
        
        # Register a test route
        async def test_handler(request):
            return {'status': 'ok'}
        
        route_info = RouteInfo(
            path='/health-test',
            handler=test_handler,
            methods=['GET'],
            route_type=RouteType.HTTP,
            auth=AuthType.NONE,
            scope=RouteScope.SYSTEM,
            metadata={},
            source='test'
        )
        
        registry = get_system_route_registry()
        await registry.register_route(route_info)
        
        # Perform health check
        health_checker = get_health_checker()
        health_result = await health_checker.check_registry_health(registry)
        
        assert 'status' in health_result
        assert 'details' in health_result
    
    @pytest.mark.asyncio
    async def test_route_discovery(self):
        """Test route discovery functionality"""
        from ..discovery import get_route_discovery_service
        
        # This is a simplified test - in reality you'd test with actual modules
        discovery_service = get_route_discovery_service()
        
        # Test that discovery service is available
        assert discovery_service is not None
        
        # Test discovery from non-existent source
        routes = await discovery_service.discover_routes('non.existent.module', RouteScope.SYSTEM)
        assert isinstance(routes, list)
    
    @pytest.mark.asyncio
    async def test_fastapi_adapter(self):
        """Test FastAPI adapter functionality"""
        from ..adapters.fastapi import FastAPIRouteAdapter
        
        # Create test route
        async def test_handler(request):
            return {'message': 'test'}
        
        route_info = RouteInfo(
            path='/adapter-test',
            handler=test_handler,
            methods=['GET'],
            route_type=RouteType.HTTP,
            auth=AuthType.NONE,
            scope=RouteScope.SYSTEM,
            metadata={},
            source='test'
        )
        
        # Test adapter
        adapter = FastAPIRouteAdapter()
        assert adapter.supports_framework('fastapi')
        
        route_config = await adapter.adapt_route(route_info)
        assert 'path' in route_config
        assert 'endpoint' in route_config
        assert 'methods' in route_config
    
    @pytest.mark.asyncio
    async def test_system_stats(self):
        """Test system statistics"""
        manager = get_http_system_manager()
        
        # Initialize system
        await initialize_http_system()
        
        # Get stats
        stats = await manager.get_system_stats()
        
        assert 'system_registry' in stats
        assert 'middleware' in stats
    
    @pytest.mark.asyncio
    async def test_decorator_functionality(self):
        """Test that decorators still work after refactoring"""
        # Test route decorator
        @route('/decorated', methods=['POST'], auth=AuthType.USER)
        async def decorated_handler(request):
            return {'decorated': True}
        
        # Verify decorator added metadata
        assert hasattr(decorated_handler, '_route_metadata')
        metadata = decorated_handler._route_metadata
        assert metadata['path'] == '/decorated'
        assert metadata['methods'] == ['POST']
        assert metadata['auth'] == AuthType.USER
        
        # Test systemRoute decorator
        @systemRoute('/system-decorated', methods=['GET'])
        async def system_decorated_handler(request):
            return {'system': True}
        
        # Verify system route was registered
        registry = get_system_route_registry()
        routes = await registry.get_routes()
        
        # Note: The decorator registers the route, so it should be in the registry
        # This test verifies the decorator integration works


class TestBackwardCompatibility:
    """Test backward compatibility with existing code"""
    
    @pytest.mark.asyncio
    async def test_legacy_imports(self):
        """Test that legacy imports still work"""
        try:
            # These should still work for backward compatibility
            from .. import route, systemRoute, Controller
            from .. import JsonRpcHandler, AuthType
            
            assert route is not None
            assert systemRoute is not None
            assert Controller is not None
            assert JsonRpcHandler is not None
            assert AuthType is not None
            
        except ImportError as e:
            pytest.fail(f"Legacy import failed: {e}")
    
    def test_controller_inheritance(self):
        """Test that controller inheritance still works"""
        class LegacyController(Controller):
            def test_method(self):
                return "legacy works"
        
        controller = LegacyController()
        result = controller.test_method()
        assert result == "legacy works"
    
    @pytest.mark.asyncio
    async def test_route_decorator_compatibility(self):
        """Test route decorator backward compatibility"""
        # This should work exactly like before
        @route('/legacy-route', type=RouteType.HTTP, auth=AuthType.PUBLIC, methods=['GET'])
        async def legacy_route_handler(request):
            return "legacy route"
        
        # Verify it has the expected metadata
        assert hasattr(legacy_route_handler, '_route_metadata')
        metadata = legacy_route_handler._route_metadata
        assert metadata['path'] == '/legacy-route'
        assert metadata['type'] == RouteType.HTTP
        assert metadata['auth'] == AuthType.PUBLIC
