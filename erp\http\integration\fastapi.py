"""
FastAPI integration utilities for HTTP routes
"""

import inspect
from typing import Dict, Any
from fastapi import APIRouter, Request

from .metadata import RouteType
from .registry import SystemRouteRegistry, get_all_database_routes
from .response_processor import FastAPIResponseProcessor
from ..logging import get_logger

logger = get_logger(__name__)


class RouteIntegration:
    """Integration layer for registering routes with FastAPI"""

    @staticmethod
    def register_routes_with_fastapi(app, system_route_registry: SystemRouteRegistry):
        """
        Register all system routes from registry with FastAPI app

        Args:
            app: FastAPI application instance
            system_route_registry: SystemRouteRegistry containing system routes to register
        """
        # Use method_routes to properly handle multiple methods per path
        method_routes = system_route_registry.get_method_routes()

        # Flatten method routes for registration
        flattened_routes = {}
        for path, route_list in method_routes.items():
            for i, route_info in enumerate(route_list):
                # Create unique key for each method-specific route
                unique_key = f"{path}#{i}" if i > 0 else path
                flattened_routes[unique_key] = route_info

        RouteIntegration._register_routes_from_registry(app, flattened_routes, "system")

    @staticmethod
    async def register_database_routes_with_fastapi(app):
        """
        Register all database-specific routes with FastAPI app

        Args:
            app: FastAPI application instance
        """
        try:
            database_routes = await get_all_database_routes()
            
            for db_name, routes in database_routes.items():
                RouteIntegration._register_routes_from_registry(app, routes, f"database_{db_name}")
                
            total_routes = sum(len(routes) for routes in database_routes.values())
            logger.info(f"Registered {total_routes} database-specific routes from {len(database_routes)} databases")
            
        except Exception as e:
            logger.error(f"Failed to register database routes: {e}")

    @staticmethod
    def _register_routes_from_registry(app, routes: Dict[str, Dict], source: str):
        """
        Register routes from a routes dictionary with FastAPI app

        Args:
            app: FastAPI application instance
            routes: Dictionary of routes to register
            source: Source identifier for logging
        """
        # Create router for HTTP routes
        http_router = APIRouter(tags=[f"http_routes_{source}"])

        # Create router for JSON RPC routes
        jsonrpc_router = APIRouter(prefix="/jsonrpc", tags=[f"jsonrpc_{source}"])

        # Get JSON RPC handler
        jsonrpc_handler = RouteIntegration._get_jsonrpc_handler()

        for path, route_info in routes.items():
            handler = route_info['handler']
            route_type = route_info.get('type', RouteType.HTTP)
            methods = route_info.get('methods', ['GET'])

            if route_type == RouteType.JSON:
                # Register as JSON RPC method
                method_name = route_info.get('jsonrpc_method', handler.__name__)
                jsonrpc_handler.register_method(method_name, handler)

                # Add JSON RPC endpoint if not already added
                if not hasattr(jsonrpc_router, '_jsonrpc_endpoint_added'):
                    @jsonrpc_router.post("")
                    @jsonrpc_router.post("/")
                    async def jsonrpc_endpoint(request):
                        return await jsonrpc_handler.handle_request(request)

                    jsonrpc_router._jsonrpc_endpoint_added = True

            else:
                # Optimized handler processing
                actual_handler = RouteIntegration._get_optimized_handler(handler, path)

                # Create FastAPI-compatible wrapper
                fastapi_handler = RouteIntegration._create_fastapi_wrapper(actual_handler)

                # Register as HTTP route
                logger.debug(f"Registering HTTP route: {methods} {path}")

                # Register the route with all specified methods at once
                http_router.add_api_route(
                    path,
                    fastapi_handler,
                    methods=[method.upper() for method in methods],
                    tags=[f"{source}_http"]
                )

        # Include routers in the main app
        app.include_router(http_router)
        app.include_router(jsonrpc_router)

        logger.debug(f"Registered {len(routes)} routes from {source}")

    @staticmethod
    def _get_jsonrpc_handler():
        """Get JSON RPC handler"""
        try:
            from .jsonrpc import get_jsonrpc_handler
            return get_jsonrpc_handler()
        except ImportError:
            logger.warning("JSON RPC handler not available")
            return None

    @staticmethod
    def _get_optimized_handler(handler, path: str):
        """Get optimized handler for route registration"""
        # Check if handler needs controller instantiation
        if hasattr(handler, '_route_metadata'):
            original_func = handler._route_metadata.get('original_func')
            if original_func and hasattr(original_func, '__qualname__'):
                qualname_parts = original_func.__qualname__.split('.')
                if len(qualname_parts) > 1:
                    try:
                        return RouteIntegration._create_controller_handler(original_func, qualname_parts, path)
                    except Exception as e:
                        logger.debug(f"Could not create controller handler for {path}: {e}")

        return handler

    @staticmethod
    def _create_controller_handler(original_func, qualname_parts, path: str):
        """Create optimized controller handler"""
        module = inspect.getmodule(original_func)
        if not module:
            return None

        class_name = qualname_parts[-2]
        controller_class = getattr(module, class_name, None)

        if controller_class and hasattr(controller_class, '__bases__'):
            try:
                from ..controllers import Controller
                if issubclass(controller_class, Controller):
                    controller_instance = controller_class()
                    method_name = qualname_parts[-1]
                    bound_method = getattr(controller_instance, method_name)

                    async def controller_wrapper(request):
                        if inspect.iscoroutinefunction(bound_method):
                            return await bound_method(request)
                        else:
                            return bound_method(request)

                    return controller_wrapper
            except ImportError:
                logger.debug("Controller class not available")

        return None

    @staticmethod
    def _create_fastapi_wrapper(handler):
        """Create FastAPI-compatible wrapper with proper response handling"""
        # Check if handler expects a request parameter
        sig = inspect.signature(handler)
        expects_request = len(sig.parameters) > 0

        async def fastapi_endpoint(request: Request):
            # Call handler with or without request parameter based on signature
            if expects_request:
                result = await handler(request)
            else:
                result = await handler()

            # Process result using response processor
            return FastAPIResponseProcessor.create_fastapi_response(result)

        fastapi_endpoint.__name__ = getattr(handler, '__name__', 'unknown_handler')
        fastapi_endpoint.__doc__ = getattr(handler, '__doc__', None)

        return fastapi_endpoint


def setup_http_routes(app):
    """
    Setup HTTP routes with FastAPI application
    Only registers system routes, NO database routes during server startup

    Args:
        app: FastAPI application instance
    """
    from ..registries import get_system_route_registry
    
    # Register ONLY system routes during server startup
    RouteIntegration.register_routes_with_fastapi(app, get_system_route_registry())

    # Database-specific routes will be registered lazily when databases are accessed
    # NO database route registration during server startup
