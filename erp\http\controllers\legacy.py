"""
Odoo-style controller base class for ERP HTTP routing
"""

from typing import Any, Dict, Optional, Type, List
from fastapi import Request
from fastapi.responses import Response, HTMLResponse, JSONResponse, RedirectResponse

from ..logging import get_logger
from ..templates.manager import get_template_manager
from ..context import ContextManager
from ..config import config

logger = get_logger(__name__)


class Controller:
    """
    Base controller class for Odoo-style controllers
    
    Provides common functionality for handling HTTP requests,
    template rendering, and response generation.
    """
    
    def __init__(self):
        self.template_manager = get_template_manager()
        self._current_request = None
        self._current_env = None
    
    @property
    def env(self):
        """
        Get current environment (Odoo-style access)
        
        Returns:
            Environment: Current environment from context or None
        """
        return self._current_env or self.get_current_environment()
    
    @property
    def request(self):
        """
        Get current request object
        
        Returns:
            Request: Current FastAPI request object or None
        """
        return self._current_request
    
    @property
    def params(self):
        """
        Get request parameters (query params + form data)
        
        Returns:
            dict: Combined query parameters and form data
        """
        if not self.request:
            return {}
        
        # Combine query params and form data
        params = dict(self.request.query_params)
        
        # Add form data if available (for POST requests)
        if hasattr(self.request, '_form_data'):
            params.update(self.request._form_data)
        
        return params
    
    @property
    def headers(self):
        """
        Get request headers
        
        Returns:
            dict: Request headers
        """
        if not self.request:
            return {}
        
        return dict(self.request.headers)
    
    def get_param(self, key: str, default=None):
        """
        Get a specific parameter value
        
        Args:
            key: Parameter name
            default: Default value if parameter not found
            
        Returns:
            Parameter value or default
        """
        return self.params.get(key, default)
    
    def get_header(self, key: str, default=None):
        """
        Get a specific header value (case-insensitive)
        
        Args:
            key: Header name (case-insensitive)
            default: Default value if header not found
            
        Returns:
            Header value or default
        """
        if not self.request:
            return default
        
        # FastAPI headers are case-insensitive
        return self.request.headers.get(key, default)
    
    def _set_request_context(self, request, env=None):
        """
        Internal method to set request and environment context
        
        Args:
            request: FastAPI request object
            env: Environment object (optional)
        """
        self._current_request = request
        self._current_env = env
    
    def _clear_request_context(self):
        """
        Internal method to clear request context
        """
        self._current_request = None
        self._current_env = None
    
    async def render_template(self, template_name: str, context: Dict[str, Any] = None) -> HTMLResponse:
        """
        Render a template with the given context

        Args:
            template_name: Name of the template to render (e.g., 'system.database_list_enhanced')
            context: Template context variables

        Returns:
            HTMLResponse with rendered template
        """
        if context is None:
            context = {}

        try:
            # Template will be loaded automatically if not found
            html_content = await self.template_manager.render_template_async(template_name, context)
            return HTMLResponse(content=html_content)

        except Exception as e:
            logger.error(f"Error rendering template {template_name}: {e}")
            # Fallback to error template
            try:
                error_context = {
                    'title': 'Template Error',
                    'error_message': str(e),
                    'template_name': template_name
                }
                error_html = await self.template_manager.render_template_async('error.html', error_context)
                return HTMLResponse(content=error_html, status_code=500)
            except:
                # Final fallback
                return HTMLResponse(
                    content=f"<html><body><h1>Error</h1><p>Template error: {str(e)}</p></body></html>",
                    status_code=500
                )
    
    def json_response(self, data: Any, status_code: int = 200) -> JSONResponse:
        """
        Create a JSON response
        
        Args:
            data: Data to serialize to JSON
            status_code: HTTP status code
            
        Returns:
            JSONResponse
        """
        return JSONResponse(content=data, status_code=status_code)
    
    def redirect(self, url: str, status_code: int = 302) -> RedirectResponse:
        """
        Create a redirect response
        
        Args:
            url: URL to redirect to
            status_code: HTTP status code (302 or 301)
            
        Returns:
            RedirectResponse
        """
        return RedirectResponse(url=url, status_code=status_code)
    
    def get_current_environment(self):
        """Get current environment from context"""
        return ContextManager.get_environment()
    
    def get_current_user(self):
        """Get current user from environment"""
        return self.env.uid if self.env else None
    
    def get_current_database(self):
        """Get current database from environment"""
        return self.env.cr.db_name if self.env else None
    
    async def get_database_list(self) -> List[Dict[str, Any]]:
        """Get list of available databases"""
        from ..routes.database import get_database_list
        return await get_database_list()
    
    def check_database_access(self, db_name: str) -> bool:
        """
        Check if database is accessible based on filter
        
        Args:
            db_name: Database name to check
            
        Returns:
            True if accessible, False otherwise
        """
        from ..database.memory import DatabaseFilterProcessor
        return DatabaseFilterProcessor.check_database_matches_filter(db_name, config.db_filter)
    
    def get_database_from_request(self, request: Request) -> Optional[str]:
        """
        Get database name from request (query param or cookie)
        
        Args:
            request: FastAPI request object
            
        Returns:
            Database name or None
        """
        # Check query parameter first
        db_from_query = request.query_params.get('db')
        if db_from_query:
            return db_from_query
        
        # Check cookie
        db_from_cookie = request.cookies.get('erp_database')
        if db_from_cookie:
            return db_from_cookie
        
        return None
    
    async def should_redirect_to_db_list(self, request: Request) -> bool:
        """
        Determine if request should be redirected to database list
        
        Args:
            request: FastAPI request object
            
        Returns:
            True if should redirect to database list
        """
        # If list_db is disabled, never redirect
        if not config.list_db:
            return False
        
        # Get database from request
        db_name = self.get_database_from_request(request)
        
        # If no database specified, redirect to list
        if not db_name:
            return True
        
        # If database filter is set, check if it matches exactly one database
        if config.db_filter:
            from ..database.memory import DatabaseFilterProcessor
            databases = await self.get_database_list()
            db_names = [db['name'] for db in databases]
            accessible_dbs = DatabaseFilterProcessor.filter_databases(db_names, config.db_filter)
            
            # If filter doesn't match exactly one database, redirect to list
            if len(accessible_dbs) != 1:
                return True
            
            # If specified database is not the filtered one, redirect
            if accessible_dbs[0] != db_name:
                return True
        
        return False


class ControllerRegistry:
    """Registry for managing controller classes"""
    
    def __init__(self):
        self._controllers: Dict[str, Type[Controller]] = {}
    
    def register_controller(self, name: str, controller_class: Type[Controller]):
        """Register a controller class"""
        self._controllers[name] = controller_class
        logger.debug(f"Registered controller: {name}")
    
    def get_controller(self, name: str) -> Optional[Type[Controller]]:
        """Get a controller class by name"""
        return self._controllers.get(name)
    
    def get_all_controllers(self) -> Dict[str, Type[Controller]]:
        """Get all registered controllers"""
        return self._controllers.copy()


# Global controller registry
_controller_registry = ControllerRegistry()


def get_controller_registry() -> ControllerRegistry:
    """Get the global controller registry"""
    return _controller_registry
